name: Deploy ECS Cluster Infrastructure

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment (nonprod, prod)'
        required: true
        default: 'nonprod'
        type: choice
        options:
          - nonprod
          - prod
      region:
        description: 'AWS region'
        required: true
        default: 'us-east-1'
        type: choice
        options:
          - us-east-1
          - us-west-2
      component_id:
        description: 'Component ID for tagging'
        required: true
        type: string
      terraform_action:
        description: 'Terraform action to perform'
        required: true
        default: 'plan'
        type: choice
        options:
          - plan
          - apply
          - destroy
      auto_approve:
        description: 'Auto-approve Terraform apply (use with caution)'
        required: false
        default: false
        type: boolean
  push:
    branches:
      - main
      - develop
    paths:
      - 'separated-infrastructure/cluster-infrastructure/**'
      - '.github/workflows/deploy-cluster-infrastructure.yml'
  pull_request:
    branches:
      - main
      - develop
    paths:
      - 'separated-infrastructure/cluster-infrastructure/**'
      - '.github/workflows/deploy-cluster-infrastructure.yml'

env:
  TF_VERSION: '1.5.7'
  AWS_DEFAULT_REGION: ${{ inputs.region || 'us-east-1' }}
  ENVIRONMENT: ${{ inputs.environment || 'nonprod' }}

jobs:
  validate:
    name: Validate Terraform Configuration
    runs-on: ubuntu-latest
    outputs:
      tf-fmt-check: ${{ steps.fmt.outputs.exitcode }}
      tf-validate-check: ${{ steps.validate.outputs.exitcode }}
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Terraform Format Check
        id: fmt
        run: |
          cd separated-infrastructure/cluster-infrastructure
          terraform fmt -check=true -diff=true
        continue-on-error: true

      - name: Terraform Init
        run: |
          cd separated-infrastructure/cluster-infrastructure
          terraform init -backend=false

      - name: Terraform Validate
        id: validate
        run: |
          cd separated-infrastructure/cluster-infrastructure
          terraform validate

      - name: Validation Summary
        run: |
          echo "## Terraform Validation Results" >> $GITHUB_STEP_SUMMARY
          echo "| Check | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|-------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Format | ${{ steps.fmt.outcome == 'success' && '✅ Passed' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Validate | ${{ steps.validate.outcome == 'success' && '✅ Passed' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: validate
    if: always()
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Run Checkov Security Scan
        uses: bridgecrewio/checkov-action@master
        with:
          directory: separated-infrastructure/cluster-infrastructure
          framework: terraform
          output_format: sarif
          output_file_path: checkov-results.sarif
          soft_fail: true

      - name: Upload Checkov Results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: checkov-results.sarif

  plan:
    name: Terraform Plan
    runs-on: ubuntu-latest
    needs: [validate, security-scan]
    if: always() && needs.validate.result == 'success'
    environment: ${{ inputs.environment || 'nonprod' }}
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_DEFAULT_REGION }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Create Terraform Variables File
        run: |
          cd separated-infrastructure/cluster-infrastructure
          cat > terraform.tfvars << EOF
          # Essential Configuration (Only required inputs)
          environment   = "${{ env.ENVIRONMENT }}"
          region        = "${{ env.AWS_DEFAULT_REGION }}"
          component_id  = "${{ inputs.component_id }}"

          # Optional Overrides (Auto-resolved if not provided)
          build_number  = "${{ github.run_number }}"
          launched_by   = "github-actions"
          launched_on   = "$(date -u +%Y-%m-%d)"
          slack_contact = "${{ vars.SLACK_CONTACT || '+ais-operations' }}"

          # Optional Infrastructure Overrides (uncomment to override auto-resolution)
          # vpc_name      = "${{ vars.VPC_NAME }}"
          # asg_ami       = "${{ vars.ECS_AMI }}"
          # efs_id        = "${{ vars.EFS_ID }}"
          # efs_security_group = "${{ vars.EFS_SECURITY_GROUP }}"
          # lambda_role_arn = "${{ vars.LAMBDA_ROLE_ARN }}"
          EOF

      - name: Terraform Init
        run: |
          cd separated-infrastructure/cluster-infrastructure
          terraform init \
            -backend-config="bucket=${{ vars.TF_STATE_BUCKET }}" \
            -backend-config="key=cluster-infrastructure/${{ env.ENVIRONMENT }}/terraform.tfstate" \
            -backend-config="region=${{ env.AWS_DEFAULT_REGION }}" \
            -backend-config="dynamodb_table=${{ vars.TF_STATE_LOCK_TABLE }}"

      - name: Terraform Plan
        id: plan
        run: |
          cd separated-infrastructure/cluster-infrastructure
          terraform plan -detailed-exitcode -out=tfplan
        continue-on-error: true

      - name: Upload Plan Artifact
        uses: actions/upload-artifact@v4
        if: steps.plan.outputs.exitcode == '2'
        with:
          name: terraform-plan-${{ env.ENVIRONMENT }}-${{ github.run_number }}
          path: separated-infrastructure/cluster-infrastructure/tfplan
          retention-days: 30

      - name: Plan Summary
        run: |
          cd separated-infrastructure/cluster-infrastructure
          echo "## Terraform Plan Results" >> $GITHUB_STEP_SUMMARY
          echo "Exit Code: ${{ steps.plan.outputs.exitcode }}" >> $GITHUB_STEP_SUMMARY
          echo "- 0: No changes" >> $GITHUB_STEP_SUMMARY
          echo "- 1: Error" >> $GITHUB_STEP_SUMMARY
          echo "- 2: Changes detected" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ steps.plan.outputs.exitcode }}" == "2" ]; then
            echo "### Changes Detected ⚠️" >> $GITHUB_STEP_SUMMARY
            echo "Terraform plan artifact uploaded for review." >> $GITHUB_STEP_SUMMARY
          elif [ "${{ steps.plan.outputs.exitcode }}" == "0" ]; then
            echo "### No Changes ✅" >> $GITHUB_STEP_SUMMARY
          else
            echo "### Plan Failed ❌" >> $GITHUB_STEP_SUMMARY
          fi

  apply:
    name: Terraform Apply
    runs-on: ubuntu-latest
    needs: plan
    if: |
      always() && 
      needs.plan.result == 'success' && 
      (inputs.terraform_action == 'apply' || inputs.terraform_action == 'destroy') &&
      github.event_name == 'workflow_dispatch'
    environment: ${{ inputs.environment || 'nonprod' }}
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_DEFAULT_REGION }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Create Terraform Variables File
        run: |
          cd separated-infrastructure/cluster-infrastructure
          # Recreate the same tfvars file as in plan step
          cat > terraform.tfvars << EOF
          # Essential Configuration (Only required inputs)
          environment   = "${{ env.ENVIRONMENT }}"
          region        = "${{ env.AWS_DEFAULT_REGION }}"
          component_id  = "${{ inputs.component_id }}"

          # Optional Overrides (Auto-resolved if not provided)
          build_number  = "${{ github.run_number }}"
          launched_by   = "github-actions"
          launched_on   = "$(date -u +%Y-%m-%d)"
          slack_contact = "${{ vars.SLACK_CONTACT || '+ais-operations' }}"

          # Optional Infrastructure Overrides (uncomment to override auto-resolution)
          # vpc_name      = "${{ vars.VPC_NAME }}"
          # asg_ami       = "${{ vars.ECS_AMI }}"
          # efs_id        = "${{ vars.EFS_ID }}"
          # efs_security_group = "${{ vars.EFS_SECURITY_GROUP }}"
          # lambda_role_arn = "${{ vars.LAMBDA_ROLE_ARN }}"
          EOF

      - name: Terraform Init
        run: |
          cd separated-infrastructure/cluster-infrastructure
          terraform init \
            -backend-config="bucket=${{ vars.TF_STATE_BUCKET }}" \
            -backend-config="key=cluster-infrastructure/${{ env.ENVIRONMENT }}/terraform.tfstate" \
            -backend-config="region=${{ env.AWS_DEFAULT_REGION }}" \
            -backend-config="dynamodb_table=${{ vars.TF_STATE_LOCK_TABLE }}"

      - name: Terraform Apply
        if: inputs.terraform_action == 'apply'
        run: |
          cd separated-infrastructure/cluster-infrastructure
          if [ "${{ inputs.auto_approve }}" == "true" ]; then
            terraform apply -auto-approve
          else
            terraform apply
          fi

      - name: Terraform Destroy
        if: inputs.terraform_action == 'destroy'
        run: |
          cd separated-infrastructure/cluster-infrastructure
          if [ "${{ inputs.auto_approve }}" == "true" ]; then
            terraform destroy -auto-approve
          else
            terraform destroy
          fi

      - name: Apply Summary
        run: |
          echo "## Terraform ${{ inputs.terraform_action }} Results" >> $GITHUB_STEP_SUMMARY
          echo "Action: ${{ inputs.terraform_action }}" >> $GITHUB_STEP_SUMMARY
          echo "Environment: ${{ env.ENVIRONMENT }}" >> $GITHUB_STEP_SUMMARY
          echo "Region: ${{ env.AWS_DEFAULT_REGION }}" >> $GITHUB_STEP_SUMMARY
          echo "Component ID: ${{ inputs.component_id }}" >> $GITHUB_STEP_SUMMARY

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [validate, plan, apply]
    if: always() && github.event_name == 'workflow_dispatch'
    
    steps:
      - name: Notify Slack
        if: vars.SLACK_WEBHOOK_URL
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: ${{ vars.SLACK_CHANNEL || '#deployments' }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          text: |
            ECS Cluster Infrastructure Deployment
            Environment: ${{ env.ENVIRONMENT }}
            Action: ${{ inputs.terraform_action }}
            Status: ${{ job.status }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
