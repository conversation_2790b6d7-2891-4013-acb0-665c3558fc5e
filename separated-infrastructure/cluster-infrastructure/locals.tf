#==============================================================
# Smart Variable Resolution System
# Automatically resolves configuration values based on environment and region
#==============================================================

locals {
  # Core derived values
  region_abbreviated = lookup(var.regions_abbreviated, var.region, "unknown")
  
  # Environment-based account configuration
  account_config = var.environment == "prod" ? {
    account_type = "prod"
    account_id   = var.environment == "prod" && var.region == "us-east-1" ? "************" : 
                   var.environment == "prod" && var.region == "us-west-2" ? "************" : "************"
    account_name = var.environment == "prod" ? "awsaaia" : "awsaaianp"
  } : {
    account_type = "nonprod"
    account_id   = "************"
    account_name = "awsaaianp"
  }
  
  # VPC configuration based on environment and region
  vpc_config = var.environment == "prod" ? {
    vpc_name    = "awsaaia3"
    rr_vpc_name = "awsaaia"
  } : {
    vpc_name    = "awsaaianp2"
    rr_vpc_name = "awsaaianp"
  }
  
  # Region-specific network configuration
  network_config = var.environment == "prod" && var.region == "us-east-1" ? {
    availability_zones = ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d"]
    nfs_cidr          = "10.204.178.0/23"
  } : var.environment == "prod" && var.region == "us-west-2" ? {
    availability_zones = ["us-west-2a", "us-west-2b", "us-west-2c"]
    nfs_cidr          = "100.72.16.0/22"
  } : var.environment == "nonprod" && var.region == "us-east-1" ? {
    availability_zones = ["us-east-1a", "us-east-1b", "us-east-1c"]
    nfs_cidr          = "10.204.178.0/23"
  } : {
    availability_zones = ["us-east-1a", "us-east-1b"]
    nfs_cidr          = "10.0.0.0/16"
  }
  
  # EFS configuration based on environment and region
  efs_config = var.environment == "nonprod" && var.region == "us-east-1" ? {
    efs_id             = "fs-5fcfafaa"
    efs_security_group = "sg-0358b7ef2c51fcbd1"
  } : var.environment == "prod" && var.region == "us-east-1" ? {
    efs_id             = lookup(var.efs_shares, var.region, "")
    efs_security_group = lookup(var.efs_security_group, var.region, "")
  } : {
    efs_id             = ""
    efs_security_group = ""
  }
  
  # AMI configuration based on environment and region
  ami_config = var.environment == "nonprod" && var.region == "us-east-1" ? {
    asg_ami = lookup(var.webstack_ami_ids, var.region, "ami-0f2705ede1e949797")
  } : var.environment == "prod" && var.region == "us-east-1" ? {
    asg_ami = lookup(var.webstack_ami_ids, var.region, "ami-0abcdef1234567890")
  } : {
    asg_ami = "ami-0abcdef1234567890"  # Default ECS-optimized AMI
  }
  
  # IAM configuration
  iam_config = {
    lambda_role_arn        = local.account_config.account_type == "nonprod" ? 
                            "arn:aws:iam::${local.account_config.account_id}:role/acct-managed/ais10-lambda-role" :
                            "arn:aws:iam::${local.account_config.account_id}:role/acct-managed/ais10-lambda-role"
    iam_instance_profile   = "ais10-ec2-for-ec2ecs-role"
    ec2_key_name          = "AIS10"
  }
  
  # Package and bucket configuration
  package_config = var.environment == "nonprod" && var.region == "us-east-1" ? {
    package_bucket = "ais.1-0.application.packages.np.ue1"
    lambda_package_path = "../../artifacts/lambda/ecs-termination-protection.zip"
  } : var.environment == "prod" && var.region == "us-east-1" ? {
    package_bucket = "ais.1-0.application.packages.prod.ue1"
    lambda_package_path = "../../artifacts/lambda/ecs-termination-protection.zip"
  } : {
    package_bucket = ""
    lambda_package_path = "../../artifacts/lambda/ecs-termination-protection.zip"
  }

  # Auto-resolved final configuration values
  resolved_config = {
    # Core identifiers
    application             = coalesce(var.application, "ais10")
    application_abbreviated = coalesce(var.application_abbreviated, "a10")
    service                 = coalesce(var.service, "processing-apps")
    component               = coalesce(var.component, "processing-apps")
    region_abbreviated      = local.region_abbreviated

    # Network configuration
    vpc_name               = coalesce(var.vpc_name, local.vpc_config.vpc_name)
    homenet_cidr          = coalesce(var.homenet_cidr, "************/32")
    ais_cidr              = coalesce(var.ais_cidr, "***********/32")
    remote_cidr           = coalesce(var.remote_cidr, "**************/32")
    nfs_cidr              = coalesce(var.nfs_cidr, local.network_config.nfs_cidr)

    # ECS and infrastructure
    asg_ami               = coalesce(var.asg_ami, local.ami_config.asg_ami)
    instance_type         = coalesce(var.instance_type, "m5.4xlarge")
    efs_id                = coalesce(var.efs_id, local.efs_config.efs_id)
    efs_security_group    = coalesce(var.efs_security_group, local.efs_config.efs_security_group)

    # IAM and security
    lambda_role_arn       = coalesce(var.lambda_role_arn, local.iam_config.lambda_role_arn)
    iam_instance_profile  = coalesce(var.iam_instance_profile, local.iam_config.iam_instance_profile)
    ec2_key_name         = coalesce(var.ec2_key_name, local.iam_config.ec2_key_name)

    # Lambda configuration
    package_path_ecs_termination_protection = coalesce(
      var.package_path_ecs_termination_protection,
      local.package_config.lambda_package_path
    )

    # Account information
    account_type = local.account_config.account_type
  }
}
