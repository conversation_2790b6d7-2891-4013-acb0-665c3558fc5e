# Core Infrastructure Variables with Smart Defaults
# Note: These variables use defaults from shared-variables.*.tf files

# Essential variables that must be provided
variable "environment" {
  description = "Environment (nonprod, prod)"
  type        = string
  validation {
    condition     = contains(["nonprod", "prod"], var.environment)
    error_message = "Environment must be either 'nonprod' or 'prod'."
  }
}

variable "region" {
  description = "AWS region"
  type        = string
  validation {
    condition     = contains(["us-east-1", "us-west-2"], var.region)
    error_message = "Region must be either 'us-east-1' or 'us-west-2'."
  }
}

# Auto-resolved variables with defaults from shared variables
variable "application" {
  description = "Application name (defaults from shared variables)"
  type        = string
  default     = "ais10"  # From shared-variables.global.tf
}

variable "application_abbreviated" {
  description = "Abbreviated application name (defaults from shared variables)"
  type        = string
  default     = "a10"  # From shared-variables.global.tf
}

variable "service" {
  description = "Service name (defaults from shared variables)"
  type        = string
  default     = "processing-apps"  # Override default for processing apps
}

variable "component" {
  description = "Component name (defaults from shared variables)"
  type        = string
  default     = "processing-apps"  # Default component name
}

variable "component_id" {
  description = "Component ID for tagging (defaults from shared variables)"
  type        = string
  default     = "CI0934608"  # From shared-variables.global.tf processing_apps_component_id
}

# Auto-generated deployment metadata with smart defaults
variable "build_number" {
  description = "Build number for deployment tracking (auto-generated if not provided)"
  type        = string
  default     = "manual"  # Default for manual deployments
}

variable "launched_by" {
  description = "Who launched this deployment (auto-detected if not provided)"
  type        = string
  default     = "terraform"  # Default launcher
}

variable "launched_on" {
  description = "When this deployment was launched (auto-generated if not provided)"
  type        = string
  default     = "unknown"  # Will be overridden by timestamp in workflows
}

variable "slack_contact" {
  description = "Slack contact for notifications (defaults from shared variables)"
  type        = string
  default     = "+ais-operations"  # From shared-variables.global.tf
}

# Auto-resolved region abbreviation
variable "region_abbreviated" {
  description = "Abbreviated region name (auto-resolved from region)"
  type        = string
  default     = "ue1"  # Default to us-east-1, will be overridden by lookup
}

#==============================================================
# Auto-Resolution Logic
#==============================================================

locals {
  # Auto-resolve region abbreviation from lookup table
  resolved_region_abbreviated = lookup(var.regions_abbreviated, var.region, var.region_abbreviated)

  # Auto-resolve account type from environment
  resolved_account_type = var.environment == "prod" ? "prod" : "nonprod"

  # Auto-resolve VPC name based on environment and region
  resolved_vpc_name = var.environment == "prod" ? "awsaaia3" : "awsaaianp2"

  # Auto-resolve EFS configuration based on environment and region
  resolved_efs_id = var.environment == "nonprod" && var.region == "us-east-1" ? "fs-5fcfafaa" : lookup(var.efs_shares, var.region, var.efs_id)

  resolved_efs_security_group = var.environment == "nonprod" && var.region == "us-east-1" ? "sg-0358b7ef2c51fcbd1" : lookup(var.efs_security_group, var.region, var.efs_security_group)

  # Auto-resolve AMI based on environment and region
  resolved_asg_ami = lookup(var.webstack_ami_ids, var.region, var.asg_ami)

  # Auto-resolve Lambda role based on environment
  resolved_lambda_role_arn = var.environment == "nonprod" ? "arn:aws:iam::************:role/acct-managed/ais10-lambda-role" : var.lambda_role_arn
}

# Network Configuration with Smart Defaults
# Note: These variables use defaults from shared-variables.*.tf files

variable "vpc_name" {
  description = "Name of the VPC to use (auto-resolved from shared variables)"
  type        = string
  default     = "awsaaianp2"  # Default for nonprod, will be overridden by shared variables
}

variable "homenet_cidr" {
  description = "CIDR block for home network access (defaults from shared variables)"
  type        = string
  default     = "************/32"  # From shared-variables.global.tf
}

variable "ais_cidr" {
  description = "CIDR block for AIS network access (defaults from shared variables)"
  type        = string
  default     = "***********/32"  # From shared-variables.global.tf
}

variable "remote_cidr" {
  description = "CIDR block for remote network access (defaults from shared variables)"
  type        = string
  default     = "**************/32"  # From shared-variables.global.tf
}

variable "nfs_cidr" {
  description = "CIDR block for NFS access (auto-resolved from shared variables)"
  type        = string
  default     = "************/23"  # Default for nonprod us-east-1, will be overridden by shared variables
}

# ECS Configuration
variable "ecs_logging" {
  description = "ECS logging drivers configuration"
  type        = string
  default     = "[\"json-file\",\"awslogs\"]"
}

# Auto Scaling Group Configuration
variable "asg_min_size" {
  description = "Minimum size of the Auto Scaling Group"
  type        = number
  default     = 0
}

variable "asg_max_size" {
  description = "Maximum size of the Auto Scaling Group"
  type        = number
  default     = 10
}

variable "asg_desired_capacity" {
  description = "Desired capacity of the Auto Scaling Group"
  type        = number
  default     = 2
}

variable "asg_ami" {
  description = "AMI ID for ECS instances (auto-resolved from shared variables)"
  type        = string
  default     = "ami-0f2705ede1e949797"  # Default nonprod us-east-1, will be overridden by shared variables
}

variable "instance_type" {
  description = "EC2 instance type for ECS instances"
  type        = string
  default     = "m5.4xlarge"
}

variable "iam_instance_profile" {
  description = "IAM instance profile for ECS instances"
  type        = string
  default     = "ais10-ec2-for-ec2ecs-role"
}

variable "ec2_key_name" {
  description = "Name of the key pair to associate with EC2 instances"
  type        = string
  default     = "AIS10"
}

# EFS Configuration with Smart Defaults
variable "efs_id" {
  description = "EFS file system ID (auto-resolved from shared variables)"
  type        = string
  default     = "fs-5fcfafaa"  # Default nonprod us-east-1, will be overridden by shared variables
}

variable "efs_security_group" {
  description = "Security group ID for EFS access (auto-resolved from shared variables)"
  type        = string
  default     = "sg-0358b7ef2c51fcbd1"  # Default nonprod us-east-1, will be overridden by shared variables
}

# Auto Scaling Policy Configuration
variable "asp_scale_out_adjustment" {
  description = "Number of instances to add when scaling out"
  type        = number
  default     = 1
}

variable "asp_scale_out_cooldown" {
  description = "Cooldown period for scale out policy"
  type        = number
  default     = 300
}

variable "asp_scale_in_adjustment" {
  description = "Number of instances to remove when scaling in"
  type        = number
  default     = -1
}

variable "asp_scale_in_cooldown" {
  description = "Cooldown period for scale in policy"
  type        = number
  default     = 300
}

# Auto Scaling Schedule Configuration
variable "asg_scheduling_enabled" {
  description = "Enable/disable auto scaling scheduling"
  type        = string
  default     = "false"
}

variable "asg_scheduling_normal_map" {
  description = "Map of normal scheduling configurations"
  type        = map(string)
  default = {
    "default.morning" = "0 8 * * MON-FRI"
    "default.night"   = "0 20 * * MON-FRI"
  }
}

variable "asg_extended_scheduling_enabled" {
  description = "Enable/disable extended auto scaling scheduling"
  type        = string
  default     = "false"
}

variable "asg_scheduling_extended_map" {
  description = "Map of extended scheduling configurations"
  type        = map(string)
  default = {
    "default.morning" = "0 6 * * MON-FRI"
    "default.night"   = "0 22 * * MON-FRI"
  }
}

# CloudWatch Alarm Configuration - CPU
variable "cw_alarm_low_cpu_threshold" {
  description = "Low CPU threshold for CloudWatch alarm"
  type        = number
  default     = 20
}

variable "cw_alarm_low_cpu_period" {
  description = "Period for low CPU alarm"
  type        = number
  default     = 300
}

variable "cw_alarm_low_cpu_evaluation_periods" {
  description = "Evaluation periods for low CPU alarm"
  type        = number
  default     = 2
}

variable "cw_alarm_high_cpu_threshold" {
  description = "High CPU threshold for CloudWatch alarm"
  type        = number
  default     = 80
}

variable "cw_alarm_high_cpu_period" {
  description = "Period for high CPU alarm"
  type        = number
  default     = 300
}

variable "cw_alarm_high_cpu_evaluation_periods" {
  description = "Evaluation periods for high CPU alarm"
  type        = number
  default     = 2
}

variable "cw_alarm_high_cpu_datapoint" {
  description = "Datapoints to alarm for high CPU"
  type        = number
  default     = 2
}

variable "cw_alarm_high_cpu_failsafe_threshold" {
  description = "High CPU failsafe threshold"
  type        = number
  default     = 90
}

variable "cw_alarm_high_cpu_failsafe_evaluation_periods" {
  description = "Evaluation periods for high CPU failsafe alarm"
  type        = number
  default     = 1
}

variable "cw_alarm_high_cpu_failsafe_datapoint" {
  description = "Datapoints to alarm for high CPU failsafe"
  type        = number
  default     = 1
}

# CloudWatch Alarm Configuration - Memory
variable "cw_alarm_low_mem_threshold" {
  description = "Low memory threshold for CloudWatch alarm"
  type        = number
  default     = 20
}

variable "cw_alarm_low_mem_period" {
  description = "Period for low memory alarm"
  type        = number
  default     = 300
}

variable "cw_alarm_low_mem_evaluation_periods" {
  description = "Evaluation periods for low memory alarm"
  type        = number
  default     = 2
}

variable "cw_alarm_high_mem_threshold" {
  description = "High memory threshold for CloudWatch alarm"
  type        = number
  default     = 80
}

variable "cw_alarm_high_mem_period" {
  description = "Period for high memory alarm"
  type        = number
  default     = 300
}

variable "cw_alarm_high_mem_evaluation_periods" {
  description = "Evaluation periods for high memory alarm"
  type        = number
  default     = 2
}

variable "cw_alarm_high_mem_failsafe_threshold" {
  description = "High memory failsafe threshold"
  type        = number
  default     = 90
}

variable "cw_alarm_high_mem_failsafe_evaluation_periods" {
  description = "Evaluation periods for high memory failsafe alarm"
  type        = number
  default     = 1
}

# Lambda Configuration with Smart Defaults
variable "package_path_ecs_termination_protection" {
  description = "Path to the ECS termination protection Lambda package (auto-resolved)"
  type        = string
  default     = "../../artifacts/lambda/ecs-termination-protection.zip"  # Default relative path
}

variable "lambda_role_arn" {
  description = "ARN of the IAM role for Lambda function (auto-resolved from shared variables)"
  type        = string
  default     = "arn:aws:iam::************:role/acct-managed/ais10-lambda-role"  # Default nonprod, will be overridden by shared variables
}

variable "account_type" {
  description = "Account type (auto-resolved from environment)"
  type        = string
  default     = "nonprod"  # Default account type, will be overridden based on environment
}
