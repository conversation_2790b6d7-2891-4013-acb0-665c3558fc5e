# Lambda function for ECS termination protection
resource "aws_lambda_function" "ecs_termination_protection" {
  filename         = local.resolved_config.package_path_ecs_termination_protection
  function_name    = "${local.resolved_config.application}_${var.environment}_ecs_term_protection"
  description      = "Processes lifecycle events for ${local.resolved_config.application} ${var.environment} ec2s, check for the running task and delay the termination process until all the running task get completed."
  role             = local.resolved_config.lambda_role_arn
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  source_code_hash = filebase64sha256(local.resolved_config.package_path_ecs_termination_protection)
  memory_size      = 128
  timeout          = 900

  environment {
    variables = {
      Environment = var.environment
    }
  }

  tags = {
    Application     = local.resolved_config.application
    Environment     = var.environment
    Component       = local.resolved_config.component
    Service         = local.resolved_config.service
    Release         = coalesce(var.build_number, "unknown")
    LaunchedBy      = coalesce(var.launched_by, "terraform")
    LaunchedOn      = coalesce(var.launched_on, formatdate("YYYY-MM-DD", timestamp()))
    SlackContact    = coalesce(var.slack_contact, "+ais-operations")
    Name            = "lm-${local.resolved_config.region_abbreviated}-${local.resolved_config.application_abbreviated}-${local.resolved_config.service}-${var.environment}-${coalesce(var.build_number, "unknown")}"
    ApplicationPart = "ECSTerminationProtection"
  }
}

# CloudWatch Log Group for Lambda
resource "aws_cloudwatch_log_group" "lambda_logs" {
  name              = "/aws/lambda/${local.resolved_config.application}-${var.environment}-ecs-term-Protection"
  retention_in_days = 7
}

# CloudWatch Event Rule for Auto Scaling lifecycle events
resource "aws_cloudwatch_event_rule" "autoscaling_lifecycle" {
  name        = "${local.resolved_config.application}-${var.environment}-ecs-term-protection"
  description = "Capture autoscale lifecycle events for ${local.resolved_config.application} ${var.environment} and send them to lambda to handle"

  lifecycle {
    create_before_destroy = true
  }

  event_pattern = jsonencode({
    source      = ["aws.autoscaling"]
    detail-type = ["EC2 Instance-terminate Lifecycle Action"]
    detail = {
      AutoScalingGroupName = ["${local.resolved_config.application}-${var.environment}-${local.resolved_config.component}-asg"]
    }
  })
}

# CloudWatch Event Target to trigger Lambda
resource "aws_cloudwatch_event_target" "lambda_target" {
  rule = aws_cloudwatch_event_rule.autoscaling_lifecycle.name
  arn  = aws_lambda_function.ecs_termination_protection.arn
}

# Lambda permission to allow CloudWatch Events to invoke the function
resource "aws_lambda_permission" "allow_cloudwatch" {
  statement_id  = "${aws_cloudwatch_event_rule.autoscaling_lifecycle.name}_InvokePermission"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.ecs_termination_protection.arn
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.autoscaling_lifecycle.arn
}
