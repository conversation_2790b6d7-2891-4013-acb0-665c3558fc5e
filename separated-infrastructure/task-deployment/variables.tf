# Core Configuration Variables with Smart Defaults
# Note: These variables use defaults from shared-variables.*.tf files

# Essential variables that must be provided
variable "environment" {
  description = "Environment (nonprod, prod)"
  type        = string
  validation {
    condition     = contains(["nonprod", "prod"], var.environment)
    error_message = "Environment must be either 'nonprod' or 'prod'."
  }
}

variable "region" {
  description = "AWS region"
  type        = string
  validation {
    condition     = contains(["us-east-1", "us-west-2"], var.region)
    error_message = "Region must be either 'us-east-1' or 'us-west-2'."
  }
}

variable "image_url_name_tag" {
  description = "Full URL of the container image including tag"
  type        = string
}

# Auto-resolved variables with defaults from shared variables
variable "application" {
  description = "Application name (defaults from shared variables)"
  type        = string
  default     = "ais10"  # From shared-variables.global.tf
}

variable "service" {
  description = "Service name (defaults from shared variables)"
  type        = string
  default     = "processing-apps"  # Override default for processing apps
}

variable "component" {
  description = "Component name (defaults from shared variables)"
  type        = string
  default     = "processing-apps"  # Default component name
}

variable "component_id" {
  description = "Component ID for tagging (defaults from shared variables)"
  type        = string
  default     = "CI0934608"  # From shared-variables.global.tf processing_apps_component_id
}

# Auto-generated deployment metadata with smart defaults
variable "build_number" {
  description = "Build number for deployment tracking (auto-generated if not provided)"
  type        = string
  default     = "manual"  # Default for manual deployments
}

variable "launched_by" {
  description = "Who launched this deployment (auto-detected if not provided)"
  type        = string
  default     = "terraform"  # Default launcher
}

variable "launched_on" {
  description = "When this deployment was launched (auto-generated if not provided)"
  type        = string
  default     = "unknown"  # Will be overridden by timestamp in workflows
}

variable "slack_contact" {
  description = "Slack contact for notifications (defaults from shared variables)"
  type        = string
  default     = "+ais-operations"  # From shared-variables.global.tf
}

# Cluster Configuration with Smart Defaults
variable "use_remote_state" {
  description = "Whether to use Terraform remote state to get cluster information"
  type        = bool
  default     = true
}

# Option 1: Remote State Configuration with Auto-Resolution
variable "cluster_state_bucket" {
  description = "S3 bucket containing cluster Terraform state (auto-resolved if not provided)"
  type        = string
  default     = "terraform-state-awsaaianp"  # Default nonprod bucket, will be auto-resolved
}

variable "cluster_state_key" {
  description = "S3 key for cluster Terraform state (auto-resolved if not provided)"
  type        = string
  default     = "cluster-infrastructure/nonprod/terraform.tfstate"  # Default path, will be auto-resolved
}

# Option 2: Direct Cluster Configuration (fallback)
variable "cluster_arn" {
  description = "ARN of the ECS cluster (used when use_remote_state is false)"
  type        = string
  default     = ""
}

variable "vpc_id" {
  description = "VPC ID (used when use_remote_state is false)"
  type        = string
  default     = ""
}

# Task Definition Configuration with Smart Defaults
variable "task_friendly_name" {
  description = "Friendly name for the task (defaults to component name if not provided)"
  type        = string
  default     = "processing-apps"  # Default task name
}

variable "country_iso_code" {
  description = "Country ISO code for the task"
  type        = string
  default     = "US"
}

variable "container_definition_path" {
  description = "Path to the container definition JSON file (auto-resolved if not provided)"
  type        = string
  default     = "./container-definitions/processing-apps.json"  # Default path based on component
}

variable "task_role_arn" {
  description = "ARN of the IAM role for the task (auto-resolved from shared variables)"
  type        = string
  default     = "arn:aws:iam::************:role/acct-managed/ais10-ecs-task-role"  # Default nonprod, will be auto-resolved
}

variable "execution_role_arn" {
  description = "ARN of the IAM role for task execution (auto-resolved from shared variables)"
  type        = string
  default     = "arn:aws:iam::************:role/acct-managed/ais10-ecs-execution-role"  # Default nonprod, will be auto-resolved
}

variable "requires_compatibilities" {
  description = "Launch type compatibility (EC2 or FARGATE)"
  type        = string
  default     = "EC2"
}

variable "launch_type" {
  description = "Launch type for the task (EC2 or FARGATE)"
  type        = string
  default     = "EC2"
}

# Container Configuration with Smart Defaults
variable "dotnet_env" {
  description = "DotNet environment configuration (auto-resolved from environment)"
  type        = string
  default     = "Development"  # Default .NET environment, will be auto-resolved
}

# Application-specific Configuration with Smart Defaults
variable "ini_bucket" {
  description = "S3 bucket for configuration files (auto-resolved from shared variables)"
  type        = string
  default     = "ais.1-0.application.packages.np.ue1"  # Default nonprod us-east-1, will be auto-resolved
}

variable "rds_backup_bucket" {
  description = "S3 bucket for RDS backups (auto-resolved from shared variables)"
  type        = string
  default     = "ais.1-0.rds.backups.np.ue1"  # Default nonprod us-east-1, will be auto-resolved
}

variable "rrri_topic_arn" {
  description = "ARN of the RRRI SNS topic (auto-resolved from shared variables)"
  type        = string
  default     = "arn:aws:sns:us-east-1:************:rrri-topic-nonprod"  # Default nonprod us-east-1, will be auto-resolved
}

# Logging Configuration
variable "log_retention_in_days" {
  description = "CloudWatch log retention period in days"
  type        = number
  default     = 7
}

# Scheduling Configuration with Smart Defaults
variable "schedule_expression" {
  description = "CloudWatch Events schedule expression (empty string disables scheduling)"
  type        = string
  default     = ""  # Default to no scheduling
}

variable "enabled" {
  description = "Whether the scheduled task is enabled"
  type        = bool
  default     = true
}

variable "event_rule_arn" {
  description = "ARN of the IAM role for EventBridge to invoke ECS tasks (auto-resolved from shared variables)"
  type        = string
  default     = "arn:aws:iam::************:role/acct-managed/ais10-events-role"  # Default nonprod, will be auto-resolved
}

variable "task_count" {
  description = "Number of tasks to run when triggered"
  type        = number
  default     = 1
}

# Network Configuration (for Fargate tasks)
variable "subnet_ids" {
  description = "List of subnet IDs for Fargate tasks"
  type        = list(string)
  default     = []
}

variable "security_group_ids" {
  description = "List of security group IDs for Fargate tasks"
  type        = list(string)
  default     = []
}

variable "assign_public_ip" {
  description = "Whether to assign public IP to Fargate tasks"
  type        = bool
  default     = false
}

# Monitoring and Alerting Configuration
variable "create_failure_alarm" {
  description = "Whether to create CloudWatch alarm for task failures"
  type        = bool
  default     = true
}

variable "alarm_description" {
  description = "Description for the CloudWatch alarm"
  type        = string
  default     = ""
}

variable "alarm_metric_filter_pattern" {
  description = "Log metric filter pattern for detecting errors"
  type        = string
  default     = "[timestamp, request_id, level=\"ERROR\", ...]"
}

variable "alarm_failure_threshold" {
  description = "Threshold for failure alarm"
  type        = number
  default     = 1
}

variable "alarm_evaluation_periods" {
  description = "Number of evaluation periods for the alarm"
  type        = number
  default     = 1
}

variable "alarm_period" {
  description = "Period for alarm evaluation in seconds"
  type        = number
  default     = 300
}

variable "alarm_action_arn" {
  description = "ARN of the action to take when alarm is triggered (auto-resolved from shared variables)"
  type        = string
  default     = "arn:aws:sns:us-east-1:************:rrri-topic-nonprod"  # Default nonprod us-east-1, will be auto-resolved
}

#==============================================================
# Auto-Resolution Logic
#==============================================================

locals {
  # Auto-resolve account configuration based on environment
  account_id = var.environment == "nonprod" ? "************" : "************"  # Replace with actual prod account ID

  # Auto-resolve region abbreviation
  region_abbreviated = var.region == "us-east-1" ? "ue1" : "uw2"

  # Auto-resolve cluster state configuration
  resolved_cluster_state_bucket = var.environment == "nonprod" ? "terraform-state-awsaaianp" : "terraform-state-awsaaia"
  resolved_cluster_state_key = "cluster-infrastructure/${var.environment}/terraform.tfstate"

  # Auto-resolve IAM roles based on environment
  resolved_task_role_arn = "arn:aws:iam::${local.account_id}:role/acct-managed/ais10-ecs-task-role"
  resolved_execution_role_arn = "arn:aws:iam::${local.account_id}:role/acct-managed/ais10-ecs-execution-role"
  resolved_event_rule_arn = "arn:aws:iam::${local.account_id}:role/acct-managed/ais10-events-role"

  # Auto-resolve S3 buckets based on environment and region
  resolved_ini_bucket = var.environment == "nonprod" && var.region == "us-east-1" ? "ais.1-0.application.packages.np.ue1" : var.environment == "prod" && var.region == "us-east-1" ? "ais.1-0.application.packages.prod.ue1" : var.environment == "prod" && var.region == "us-west-2" ? "ais.1-0.application.packages.prod.uw2" : var.ini_bucket

  resolved_rds_backup_bucket = var.environment == "nonprod" && var.region == "us-east-1" ? "ais.1-0.rds.backups.np.ue1" : var.environment == "prod" && var.region == "us-east-1" ? "ais.1-0.rds.backups.prod.ue1" : var.environment == "prod" && var.region == "us-west-2" ? "ais.1-0.rds.backups.prod.uw2" : var.rds_backup_bucket

  # Auto-resolve SNS topics based on environment and region
  resolved_rrri_topic_arn = var.environment == "nonprod" && var.region == "us-east-1" ? "arn:aws:sns:${var.region}:${local.account_id}:rrri-topic-nonprod" : var.environment == "prod" && var.region == "us-east-1" ? "arn:aws:sns:${var.region}:${local.account_id}:rrri-topic-prod" : var.rrri_topic_arn

  # Auto-resolve .NET environment
  resolved_dotnet_env = var.environment == "nonprod" ? "Development" : "Production"
}