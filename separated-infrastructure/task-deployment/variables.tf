#==============================================================
# Essential Variables (Required External Inputs)
#==============================================================

variable "environment" {
  description = "Environment (nonprod, prod)"
  type        = string
  validation {
    condition     = contains(["nonprod", "prod"], var.environment)
    error_message = "Environment must be either 'nonprod' or 'prod'."
  }
}

variable "region" {
  description = "AWS region"
  type        = string
  validation {
    condition     = contains(["us-east-1", "us-west-2"], var.region)
    error_message = "Region must be either 'us-east-1' or 'us-west-2'."
  }
}

variable "component_id" {
  description = "Component ID for tagging"
  type        = string
}

variable "image_url_name_tag" {
  description = "Full URL of the container image including tag"
  type        = string
}

#==============================================================
# Optional Override Variables (Auto-resolved if not provided)
#==============================================================

variable "application" {
  description = "Application name (auto-resolved if not provided)"
  type        = string
  default     = null
}

variable "service" {
  description = "Service name (auto-resolved if not provided)"
  type        = string
  default     = null
}

variable "component" {
  description = "Component name (auto-resolved if not provided)"
  type        = string
  default     = null
}

variable "build_number" {
  description = "Build number for deployment tracking (auto-generated if not provided)"
  type        = string
  default     = null
}

variable "launched_by" {
  description = "Who launched this deployment (auto-detected if not provided)"
  type        = string
  default     = null
}

variable "launched_on" {
  description = "When this deployment was launched (auto-generated if not provided)"
  type        = string
  default     = null
}

variable "slack_contact" {
  description = "Slack contact for notifications (auto-resolved if not provided)"
  type        = string
  default     = null
}

#==============================================================
# Cluster Configuration (Auto-resolved with smart defaults)
#==============================================================

variable "use_remote_state" {
  description = "Whether to use Terraform remote state to get cluster information"
  type        = bool
  default     = true
}

# Option 1: Remote State Configuration (auto-resolved if not provided)
variable "cluster_state_bucket" {
  description = "S3 bucket containing cluster Terraform state (auto-resolved if not provided)"
  type        = string
  default     = null
}

variable "cluster_state_key" {
  description = "S3 key for cluster Terraform state (auto-resolved if not provided)"
  type        = string
  default     = null
}

# Option 2: Direct Cluster Configuration (fallback when remote state not available)
variable "cluster_arn" {
  description = "ARN of the ECS cluster (used when use_remote_state is false)"
  type        = string
  default     = null
}

variable "vpc_id" {
  description = "VPC ID (used when use_remote_state is false, auto-resolved if not provided)"
  type        = string
  default     = null
}

#==============================================================
# Task Definition Configuration (Auto-resolved with smart defaults)
#==============================================================

variable "task_friendly_name" {
  description = "Friendly name for the task (defaults to component name if not provided)"
  type        = string
  default     = null
}

variable "country_iso_code" {
  description = "Country ISO code for the task"
  type        = string
  default     = null
}

variable "container_definition_path" {
  description = "Path to the container definition JSON file (auto-resolved if not provided)"
  type        = string
  default     = null
}

variable "task_role_arn" {
  description = "ARN of the IAM role for the task (auto-resolved based on environment if not provided)"
  type        = string
  default     = null
}

variable "execution_role_arn" {
  description = "ARN of the IAM role for task execution (auto-resolved based on environment if not provided)"
  type        = string
  default     = null
}

variable "requires_compatibilities" {
  description = "Launch type compatibility (EC2 or FARGATE)"
  type        = string
  default     = null
}

variable "launch_type" {
  description = "Launch type for the task (EC2 or FARGATE)"
  type        = string
  default     = null
}

# Container Configuration
variable "dotnet_env" {
  description = "DotNet environment configuration (auto-resolved based on environment if not provided)"
  type        = string
  default     = null
}

#==============================================================
# Application-specific Configuration (Auto-resolved with smart defaults)
#==============================================================

variable "ini_bucket" {
  description = "S3 bucket for configuration files (auto-resolved based on environment and region if not provided)"
  type        = string
  default     = null
}

variable "rds_backup_bucket" {
  description = "S3 bucket for RDS backups (auto-resolved based on environment and region if not provided)"
  type        = string
  default     = null
}

variable "rrri_topic_arn" {
  description = "ARN of the RRRI SNS topic (auto-resolved based on environment and region if not provided)"
  type        = string
  default     = null
}

#==============================================================
# Logging Configuration (Smart defaults)
#==============================================================

variable "log_retention_in_days" {
  description = "CloudWatch log retention period in days"
  type        = number
  default     = null
}

#==============================================================
# Scheduling Configuration (Auto-resolved with smart defaults)
#==============================================================

variable "schedule_expression" {
  description = "CloudWatch Events schedule expression (empty string disables scheduling)"
  type        = string
  default     = null
}

variable "enabled" {
  description = "Whether the scheduled task is enabled"
  type        = bool
  default     = null
}

variable "event_rule_arn" {
  description = "ARN of the IAM role for EventBridge to invoke ECS tasks (auto-resolved if not provided)"
  type        = string
  default     = null
}

variable "task_count" {
  description = "Number of tasks to run when triggered"
  type        = number
  default     = null
}

#==============================================================
# Network Configuration (Auto-resolved for Fargate tasks)
#==============================================================

variable "subnet_ids" {
  description = "List of subnet IDs for Fargate tasks (auto-resolved from VPC if not provided)"
  type        = list(string)
  default     = []
}

variable "security_group_ids" {
  description = "List of security group IDs for Fargate tasks"
  type        = list(string)
  default     = []
}

variable "assign_public_ip" {
  description = "Whether to assign public IP to Fargate tasks"
  type        = bool
  default     = null
}

#==============================================================
# Monitoring and Alerting Configuration (Auto-resolved with smart defaults)
#==============================================================

variable "create_failure_alarm" {
  description = "Whether to create CloudWatch alarm for task failures"
  type        = bool
  default     = null
}

variable "alarm_description" {
  description = "Description for the CloudWatch alarm (auto-generated if not provided)"
  type        = string
  default     = null
}

variable "alarm_metric_filter_pattern" {
  description = "Log metric filter pattern for detecting errors"
  type        = string
  default     = null
}

variable "alarm_failure_threshold" {
  description = "Threshold for failure alarm"
  type        = number
  default     = null
}

variable "alarm_evaluation_periods" {
  description = "Number of evaluation periods for the alarm"
  type        = number
  default     = null
}

variable "alarm_period" {
  description = "Period for alarm evaluation in seconds"
  type        = number
  default     = null
}

variable "alarm_action_arn" {
  description = "ARN of the action to take when alarm is triggered (auto-resolved if not provided)"
  type        = string
  default     = null
}