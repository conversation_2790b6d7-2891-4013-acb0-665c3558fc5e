terraform {
  required_providers {
    aws = {
      version = "~> 3.75"
      source  = "hashicorp/aws"
    }
  }
  backend "s3" {}
}

provider "aws" {
  region = var.region

  default_tags {
    tags = {
      "coxauto:ci-id" = var.component_id
    }
  }
  ignore_tags {
    key_prefixes = ["cai:catalog"]
  }
}

# Data source to get cluster information (if using remote state)
data "terraform_remote_state" "cluster" {
  count   = var.use_remote_state ? 1 : 0
  backend = "s3"

  config = {
    bucket = local.cluster_state_config.bucket
    key    = local.cluster_state_config.key
    region = var.region
  }
}

# ECS Task Definition
resource "aws_ecs_task_definition" "main" {
  family                   = "${local.resolved_config.task_friendly_name}_${var.environment}"
  container_definitions    = data.template_file.container_definition.rendered
  task_role_arn            = local.resolved_config.task_role_arn
  execution_role_arn       = local.resolved_config.execution_role_arn
  requires_compatibilities = [local.resolved_config.requires_compatibilities]

  volume {
    name      = "aisdata"
    host_path = "/aisdata"
  }

  tags = {
    Application  = local.resolved_config.application
    Environment  = var.environment
    Service      = local.resolved_config.service
    Release      = coalesce(var.build_number, "unknown")
    LaunchedBy   = coalesce(var.launched_by, "terraform")
    LaunchedOn   = coalesce(var.launched_on, formatdate("YYYY-MM-DD", timestamp()))
    SlackContact = coalesce(var.slack_contact, "+ais-operations")
    Component    = local.resolved_config.component
    TaskName     = local.resolved_config.task_friendly_name
  }
}

# Container definition template
data "template_file" "container_definition" {
  template = file(local.resolved_config.container_definition_path)
  vars = {
    environment        = var.environment
    country_iso_code   = local.resolved_config.country_iso_code
    region             = var.region
    task_friendly_name = local.resolved_config.task_friendly_name
    image_url_name_tag = var.image_url_name_tag
    ini_bucket         = local.resolved_config.ini_bucket
    rds_backup_bucket  = local.resolved_config.rds_backup_bucket
    rrri_topic_arn     = local.resolved_config.rrri_topic_arn
    dotnet_env         = local.resolved_config.dotnet_env
  }
}

# CloudWatch Log Group for the task
resource "aws_cloudwatch_log_group" "task_logs" {
  name              = "${local.resolved_config.task_friendly_name}_${var.environment}"
  retention_in_days = local.resolved_config.log_retention_in_days

  tags = {
    Application  = local.resolved_config.application
    Environment  = var.environment
    Service      = local.resolved_config.service
    Release      = coalesce(var.build_number, "unknown")
    LaunchedBy   = coalesce(var.launched_by, "terraform")
    LaunchedOn   = coalesce(var.launched_on, formatdate("YYYY-MM-DD", timestamp()))
    SlackContact = coalesce(var.slack_contact, "+ais-operations")
    Component    = local.resolved_config.component
    TaskName     = local.resolved_config.task_friendly_name
  }
}

# EventBridge rule for scheduled execution
resource "aws_cloudwatch_event_rule" "scheduled_task" {
  count = local.resolved_config.schedule_expression != "" ? 1 : 0

  name                = "${local.resolved_config.task_friendly_name}-${var.environment}-schedule"
  description         = "Scheduled execution for ${local.resolved_config.task_friendly_name}"
  schedule_expression = local.resolved_config.schedule_expression
  state               = local.resolved_config.enabled ? "ENABLED" : "DISABLED"

  tags = {
    Application  = local.resolved_config.application
    Environment  = var.environment
    Service      = local.resolved_config.service
    Release      = coalesce(var.build_number, "unknown")
    LaunchedBy   = coalesce(var.launched_by, "terraform")
    LaunchedOn   = coalesce(var.launched_on, formatdate("YYYY-MM-DD", timestamp()))
    SlackContact = coalesce(var.slack_contact, "+ais-operations")
    Component    = local.resolved_config.component
    TaskName     = local.resolved_config.task_friendly_name
  }
}

# EventBridge target to run the ECS task
resource "aws_cloudwatch_event_target" "ecs_task" {
  count = local.resolved_config.schedule_expression != "" ? 1 : 0

  rule     = aws_cloudwatch_event_rule.scheduled_task[0].name
  arn      = local.cluster_arn
  role_arn = local.resolved_config.event_rule_arn

  ecs_target {
    task_definition_arn = aws_ecs_task_definition.main.arn
    task_count          = local.resolved_config.task_count
    launch_type         = local.resolved_config.launch_type

    # Network configuration for Fargate tasks
    dynamic "network_configuration" {
      for_each = local.resolved_config.launch_type == "FARGATE" ? [1] : []
      content {
        subnets          = local.resolved_config.subnet_ids
        security_groups  = local.resolved_config.security_group_ids
        assign_public_ip = local.resolved_config.assign_public_ip
      }
    }
  }
}

# CloudWatch Alarm for task failures
resource "aws_cloudwatch_metric_alarm" "task_failure" {
  count = local.resolved_config.create_failure_alarm ? 1 : 0

  alarm_name        = "${local.resolved_config.task_friendly_name}-${var.environment}-failures"
  alarm_description = coalesce(var.alarm_description, "Alarm for ${local.resolved_config.task_friendly_name} task failures")

  metric_name = "ErrorCount"
  namespace   = "AWS/Logs"
  statistic   = "Sum"

  comparison_operator = "GreaterThanThreshold"
  threshold           = coalesce(var.alarm_failure_threshold, 1)
  evaluation_periods  = coalesce(var.alarm_evaluation_periods, 1)
  period              = coalesce(var.alarm_period, 300)

  dimensions = {
    LogGroupName = aws_cloudwatch_log_group.task_logs.name
  }

  alarm_actions = local.resolved_config.alarm_action_arn != "" ? [local.resolved_config.alarm_action_arn] : []

  tags = {
    Application  = local.resolved_config.application
    Environment  = var.environment
    Service      = local.resolved_config.service
    Release      = coalesce(var.build_number, "unknown")
    LaunchedBy   = coalesce(var.launched_by, "terraform")
    LaunchedOn   = coalesce(var.launched_on, formatdate("YYYY-MM-DD", timestamp()))
    SlackContact = coalesce(var.slack_contact, "+ais-operations")
    Component    = local.resolved_config.component
    TaskName     = local.resolved_config.task_friendly_name
  }
}

# CloudWatch Log Metric Filter for error detection
resource "aws_cloudwatch_log_metric_filter" "error_filter" {
  count = local.resolved_config.create_failure_alarm && coalesce(var.alarm_metric_filter_pattern, "[timestamp, request_id, level=\"ERROR\", ...]") != "" ? 1 : 0

  name           = "${local.resolved_config.task_friendly_name}-${var.environment}-error-filter"
  log_group_name = aws_cloudwatch_log_group.task_logs.name
  pattern        = coalesce(var.alarm_metric_filter_pattern, "[timestamp, request_id, level=\"ERROR\", ...]")

  metric_transformation {
    name      = "ErrorCount"
    namespace = "AWS/Logs"
    value     = "1"
  }
}
