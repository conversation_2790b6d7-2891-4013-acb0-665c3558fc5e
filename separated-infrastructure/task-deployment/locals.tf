#==============================================================
# Smart Variable Resolution System for Task Deployment
# Automatically resolves configuration values based on environment and region
#==============================================================

locals {
  # Core derived values
  region_abbreviated = lookup(var.regions_abbreviated, var.region, "unknown")
  
  # Environment-based account configuration
  account_config = var.environment == "prod" ? {
    account_type = "prod"
    account_id   = var.environment == "prod" && var.region == "us-east-1" ? "************" : 
                   var.environment == "prod" && var.region == "us-west-2" ? "************" : "************"
    account_name = var.environment == "prod" ? "awsaaia" : "awsaaianp"
  } : {
    account_type = "nonprod"
    account_id   = "************"
    account_name = "awsaaianp"
  }
  
  # VPC configuration based on environment and region
  vpc_config = var.environment == "prod" ? {
    vpc_name    = "awsaaia3"
    rr_vpc_name = "awsaaia"
  } : {
    vpc_name    = "awsaaianp2"
    rr_vpc_name = "awsaaianp"
  }
  
  # IAM role configuration based on environment
  iam_config = var.environment == "nonprod" ? {
    task_role_arn      = "arn:aws:iam::${local.account_config.account_id}:role/acct-managed/ais10-ecs-task-role"
    execution_role_arn = "arn:aws:iam::${local.account_config.account_id}:role/acct-managed/ais10-ecs-execution-role"
    event_rule_arn     = "arn:aws:iam::${local.account_config.account_id}:role/acct-managed/ais10-events-role"
  } : {
    task_role_arn      = "arn:aws:iam::${local.account_config.account_id}:role/acct-managed/ais10-ecs-task-role"
    execution_role_arn = "arn:aws:iam::${local.account_config.account_id}:role/acct-managed/ais10-ecs-execution-role"
    event_rule_arn     = "arn:aws:iam::${local.account_config.account_id}:role/acct-managed/ais10-events-role"
  }
  
  # S3 bucket configuration based on environment and region
  bucket_config = var.environment == "nonprod" && var.region == "us-east-1" ? {
    ini_bucket        = "ais.1-0.application.packages.np.ue1"
    rds_backup_bucket = "ais.1-0.rds.backups.np.ue1"
  } : var.environment == "prod" && var.region == "us-east-1" ? {
    ini_bucket        = "ais.1-0.application.packages.prod.ue1"
    rds_backup_bucket = "ais.1-0.rds.backups.prod.ue1"
  } : var.environment == "prod" && var.region == "us-west-2" ? {
    ini_bucket        = "ais.1-0.application.packages.prod.uw2"
    rds_backup_bucket = "ais.1-0.rds.backups.prod.uw2"
  } : {
    ini_bucket        = ""
    rds_backup_bucket = ""
  }
  
  # SNS topic configuration based on environment and region
  sns_config = var.environment == "nonprod" && var.region == "us-east-1" ? {
    rrri_topic_arn = "arn:aws:sns:${var.region}:${local.account_config.account_id}:rrri-topic-nonprod"
  } : var.environment == "prod" && var.region == "us-east-1" ? {
    rrri_topic_arn = "arn:aws:sns:${var.region}:${local.account_config.account_id}:rrri-topic-prod"
  } : {
    rrri_topic_arn = ""
  }
  
  # Container definition path resolution
  container_definition_config = {
    container_definition_path = coalesce(
      var.container_definition_path,
      "${path.module}/container-definitions/${var.component}.json"
    )
  }
  
  # Auto-resolved final configuration values
  resolved_config = {
    # Core identifiers
    application             = coalesce(var.application, "ais10")
    service                 = coalesce(var.service, "processing-apps")
    component               = coalesce(var.component, "processing-apps")
    region_abbreviated      = local.region_abbreviated
    
    # Task configuration
    task_friendly_name      = coalesce(var.task_friendly_name, var.component)
    country_iso_code        = coalesce(var.country_iso_code, "US")
    requires_compatibilities = coalesce(var.requires_compatibilities, "EC2")
    launch_type             = coalesce(var.launch_type, "EC2")
    
    # IAM roles
    task_role_arn           = coalesce(var.task_role_arn, local.iam_config.task_role_arn)
    execution_role_arn      = coalesce(var.execution_role_arn, local.iam_config.execution_role_arn)
    event_rule_arn          = coalesce(var.event_rule_arn, local.iam_config.event_rule_arn)
    
    # S3 buckets
    ini_bucket              = coalesce(var.ini_bucket, local.bucket_config.ini_bucket)
    rds_backup_bucket       = coalesce(var.rds_backup_bucket, local.bucket_config.rds_backup_bucket)
    
    # SNS topics
    rrri_topic_arn          = coalesce(var.rrri_topic_arn, local.sns_config.rrri_topic_arn)
    
    # Container configuration
    container_definition_path = local.container_definition_config.container_definition_path
    dotnet_env              = coalesce(var.dotnet_env, title(var.environment))
    
    # Monitoring configuration
    log_retention_in_days   = coalesce(var.log_retention_in_days, 7)
    create_failure_alarm    = coalesce(var.create_failure_alarm, true)
    alarm_action_arn        = coalesce(var.alarm_action_arn, local.sns_config.rrri_topic_arn)
    
    # Scheduling configuration
    schedule_expression     = coalesce(var.schedule_expression, "")
    enabled                 = coalesce(var.enabled, true)
    task_count              = coalesce(var.task_count, 1)
    
    # Network configuration (for Fargate)
    subnet_ids              = length(var.subnet_ids) > 0 ? var.subnet_ids : data.aws_subnet_ids.private.ids
    security_group_ids      = length(var.security_group_ids) > 0 ? var.security_group_ids : []
    assign_public_ip        = coalesce(var.assign_public_ip, false)
  }
  
  # Cluster configuration resolution
  cluster_arn = var.use_remote_state ? (
    length(data.terraform_remote_state.cluster) > 0 ? 
    data.terraform_remote_state.cluster[0].outputs.cluster_arn : 
    ""
  ) : var.cluster_arn
  
  vpc_id = var.use_remote_state ? (
    length(data.terraform_remote_state.cluster) > 0 ? 
    data.terraform_remote_state.cluster[0].outputs.vpc_id : 
    data.aws_vpc.vpc.id
  ) : coalesce(var.vpc_id, data.aws_vpc.vpc.id)
  
  # Smart defaults for cluster state configuration
  cluster_state_config = {
    bucket = coalesce(var.cluster_state_bucket, "terraform-state-${local.account_config.account_name}")
    key    = coalesce(var.cluster_state_key, "cluster-infrastructure/${var.environment}/terraform.tfstate")
  }
}
